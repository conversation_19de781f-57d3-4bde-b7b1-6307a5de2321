<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng - RAG Chatbot</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        /* Thêm style riêng cho trang lựa chọn nếu cần */
        .choice-container {
            text-align: center;
            padding: 40px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #181825;
            color: #e2e8f0;
            max-width: 500px;
            margin: auto;
        }
        .choice-container h1 {
            font-size: 2rem;
            color: #ffffff;
            margin-bottom: 1.5rem;
        }
        .choice-container p {
            margin-bottom: 2rem;
            font-size: 1.1em;
        }
        .choice-button {
            display: block;
            width: 100%;
            padding: 15px 20px;
            margin-bottom: 1rem;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            box-sizing: border-box;
        }
        .choice-button-primary {
            background-color: #facc15; /* Yellow */
            color: #181825;
        }
        .choice-button-primary:hover {
            background-color: #eab308;
        }
        .choice-button-secondary {
            background-color: #4a5568; /* Gray */
            color: white;
        }
        .choice-button-secondary:hover {
            background-color: #718096;
        }
        .choice-button-register {
            background-color: #3b82f6; /* Blue */
            color: white;
        }
        .choice-button-register:hover {
            background-color: #2563eb;
        }

    </style>
</head>
<body class="auth-body">
    <div class="choice-container">
        <h1>🤖 Chào mừng đến với RAG Chatbot!</h1>
        <p>Vui lòng chọn cách bạn muốn tiếp tục:</p>

        <a href="{{ url_for('authenticate') }}" class="choice-button choice-button-primary">👤 Xác thực để cá nhân hóa</a>

        <a href="{{ url_for('start_anonymous_chat') }}" class="choice-button choice-button-secondary">💬 Chat ẩn danh</a>

        <a href="{{ url_for('register') }}" class="choice-button choice-button-register">📝 Đăng ký thông tin mới</a>

    </div>
</body>
</html>