/* ===== Reset và Font ===== */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
    background-color: #1e1e2e;
    color: #e2e8f0;
    height: 100vh;
    overflow: hidden;
    display: flex;
}

/* ===== Layout Chính ===== */
.main-container {
    display: flex;
    width: 100%;
    height: 100vh;
}

/* ===== Sidebar ===== */
.sidebar {
    width: 280px;
    background-color: #181825;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #333;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
    transition: width 0.3s ease, padding 0.3s ease, opacity 0.3s ease;
    flex-shrink: 0;
}

/* Tùy chỉnh thanh cuộn */
.sidebar::-webkit-scrollbar,
.purchase-history::-webkit-scrollbar,
.chat-history::-webkit-scrollbar {
    width: 8px;
}

.sidebar::-webkit-scrollbar-track,
.purchase-history::-webkit-scrollbar-track,
.chat-history::-webkit-scrollbar-track {
    background-color: #181825;
}

.sidebar::-webkit-scrollbar-thumb,
.purchase-history::-webkit-scrollbar-thumb,
.chat-history::-webkit-scrollbar-thumb {
    background-color: #333;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.purchase-history::-webkit-scrollbar-thumb:hover,
.chat-history::-webkit-scrollbar-thumb:hover {
    background-color: #444;
}

/* Mũi tên đầu thanh cuộn */
.sidebar::-webkit-scrollbar-button:start:decrement,
.purchase-history::-webkit-scrollbar-button:start:decrement,
.chat-history::-webkit-scrollbar-button:start:decrement {
    background: #333 url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 16 16'><path fill='rgba(255,255,255,0.6)' d='M8 4 L14 10 L2 10 Z'/></svg>") no-repeat center;
    height: 16px;
    border-radius: 3px 3px 0 0;
}

/* Mũi tên cuối thanh cuộn */
.sidebar::-webkit-scrollbar-button:end:increment,
.purchase-history::-webkit-scrollbar-button:end:increment,
.chat-history::-webkit-scrollbar-button:end:increment {
    background: #333 url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 16 16'><path fill='rgba(255,255,255,0.6)' d='M8 12 L14 6 L2 6 Z'/></svg>") no-repeat center;
    height: 16px;
    border-radius: 0 0 3px 3px;
}

/* Hover state cho mũi tên */
.sidebar::-webkit-scrollbar-button:hover,
.purchase-history::-webkit-scrollbar-button:hover,
.chat-history::-webkit-scrollbar-button:hover {
    background-color: #444;
}

.sidebar h3 {
    margin-top: 0;
    color: #facc15;
    border-bottom: 1px solid #444;
    padding-bottom: 0.5rem;
}

.sidebar h4 {
    margin: 0.8rem 0 0.4rem;
    color: #ffffff;
}

.sidebar p,
.sidebar div,
.sidebar ul,
.sidebar ol {
    font-size: 0.95em;
    line-height: 1.5;
    margin-bottom: 0.8rem;
}

.sidebar strong {
    color: #f87171;
}

/* Lịch sử đơn hàng */
.purchase-history {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #181825;
    border: 1px solid #333;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
}

.purchase-history-item {
    font-size: 0.9em;
    border-bottom: 1px dashed #444;
    padding-bottom: 0.5rem;
    margin-bottom: 0.8rem;
    display: block !important;
}

.purchase-history-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.purchase-history-item strong {
    color: #f87171;
}

.purchase-history-item span {
    display: block;
    color: #cbd5e1;
    margin-top: 0.2rem;
    font-size: 0.9em;
}

/* Nút Đăng xuất */
.logout-button {
    margin-top: auto;
    padding: 10px 15px;
    background-color: #f87171;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    width: 100%;
    text-align: center;
    font-weight: bold;
}

.logout-button:hover {
    background-color: #ef4444;
}

/* Toggle Button */
#sidebar-toggle {
    background: none;
    border: none;
    color: #facc15;
    font-size: 1.5rem;
    cursor: pointer;
    margin-bottom: 1rem;
}

/* Sidebar Collapse */
.sidebar.sidebar-collapsed {
    width: 60px;
    padding: 1rem 0.5rem;
    overflow: hidden;
}

.sidebar.sidebar-collapsed h3,
.sidebar.sidebar-collapsed h4,
.sidebar.sidebar-collapsed p,
.sidebar.sidebar-collapsed ul,
.sidebar.sidebar-collapsed ol,
.sidebar.sidebar-collapsed .logout-button {
    display: none;
}
.sidebar.sidebar-collapsed .purchase-history-item {
    display: none !important;
}

/* ===== Khu vực Chat ===== */
.chat-area {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #1e1e2e;
    overflow: hidden;
    position: relative; /* Thêm position relative để có thể định vị các phần tử con */
}

/* Tiêu đề */
.title-area {
    text-align: center;
    padding: 1rem 0;
}

.main-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ffffff;
    margin: 0;
}

/* Lịch sử chat */
.chat-history {
    flex-grow: 1;
    padding: 1rem 1.5rem;
    padding-bottom: 5rem; /* Tăng padding dưới để tạo không gian cho khu vực câu hỏi gợi ý */
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 0;
}

/* Tin nhắn */
.chat-message {
    padding: 1rem;
    border-radius: 1.25rem;
    max-width: 80%;
    font-size: 1.125rem;
    line-height: 1.6;
    word-break: break-word;
    white-space: pre-wrap;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 0.5rem 0;
    transition: background-color 0.3s;
    width: auto;
}

/* User Message */
.user-message {
    background-color: #ef4444;
    color: #ffffff;
    align-self: flex-end;
    margin-left: auto;
    border-bottom-right-radius: 0.5rem;
    flex-direction: row-reverse;
}

/* Assistant Message */
.assistant-message {
    background-color: #fcd34d;
    color: #000000;
    align-self: flex-start;
    margin-right: auto;
    border-bottom-left-radius: 0.5rem;
}

/* Message content wrapper */
.message-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Menu items in assistant message */
.assistant-message .menu-item strong,
.assistant-message .menu-product strong {
    color: #181825;
    font-weight: normal;
    display: block;
    margin-bottom: 3px;
}

.assistant-message .menu-item div,
.assistant-message .menu-product div {
    color: #181825;
    margin-left: 15px;
    margin-bottom: 10px;
}

/* Avatar/Icon chung */
.chat-message::before {
    display: inline-block;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    margin-right: 0.75rem;
    flex-shrink: 0;
    text-align: center;
    line-height: 44px;
    font-size: 26px;
    font-weight: bold;
    content: attr(data-icon);
}

/* Icon User */
.user-message::before {
    background-color: #b91c1c;
    color: #ffffff;
    content: "🧑";
}

/* Icon Assistant */
.assistant-message::before {
    background-color: #d4a100;
    color: #181825;
    content: "🤖";
}


/* Khu vực nhập liệu */
.input-area {
    display: flex;
    padding: 1rem;
    border-top: 1px solid #333;
    background-color: #181825;
    position: relative;
    z-index: 10;
    /* Không cần position: absolute vì đây là phần tử cuối cùng trong flex container */
}

.input-area input[type="text"] {
    flex-grow: 1;
    padding: 0.8rem 1rem;
    border: 1px solid #444;
    border-radius: 20px;
    margin-right: 0.5rem;
    background-color: #2a2a3a;
    color: #ffffff;
    font-size: 1rem;
}

.input-area input[type="text"]::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.input-area button {
    padding: 0.8rem 1.5rem;
    border: none;
    background-color: #facc15;
    color: #181825;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
}

.input-area button:hover {
    background-color: #eab308;
}

.input-area button:disabled {
    background-color: #555;
    cursor: not-allowed;
}

/* Microphone button */
#mic-button {
    background-color: #3b82f6;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
}

#mic-button:hover {
    background-color: #2563eb;
}

#mic-button.recording {
    background-color: #ef4444;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

/* Voice settings button */
#voice-settings-button {
    background-color: #8b5cf6;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    border: none;
    cursor: pointer;
}

#voice-settings-button:hover {
    background-color: #7c3aed;
}

/* Voice dropdown */
.voice-dropdown {
    position: relative;
    display: inline-block;
}

.voice-dropdown-content {
    display: none;
    position: absolute;
    bottom: 55px;
    right: 0;
    background-color: #1e1e2e;
    min-width: 180px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.5);
    z-index: 1;
    border-radius: 8px;
    border: 1px solid #333;
}

.voice-dropdown-content.show {
    display: block;
}

.voice-option {
    color: #e2e8f0;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    cursor: pointer;
    transition: background-color 0.2s;
}

.voice-option:hover {
    background-color: #2a2a3a;
}

.voice-option.active {
    background-color: #8b5cf6;
    color: white;
}

.voice-option.voice-test {
    border-top: 1px solid #444;
    margin-top: 8px;
    padding-top: 12px;
    color: #10b981;
    font-weight: bold;
}

/* Trang Auth */
.auth-body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    flex-direction: column;
    background-color: #1e1e2e;
}

.auth-container {
    text-align: center;
    padding: 30px;
    border: 1px solid #333;
    border-radius: 8px;
    background-color: #181825;
    color: #ffffff;
}

.auth-container h1 {
    font-size: 3rem;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.auth-container h3 {
    color: #facc15;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.auth-container video {
    border: 1px solid #444;
    margin-top: 15px;
    transform: scaleX(-1);
}

.auth-container #status {
    margin-top: 15px;
    font-weight: bold;
    font-size: 1.2rem;
    color: #f87171;
}

/* Style for the video container to position canvas on top */
.video-container {
    position: relative;
    width: 800px;
    height: 600px;
    margin: 15px auto;
}

#video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 800px;
    height: 600px;
    pointer-events: none;
    transform: scaleX(-1);
}

#video {
    display: block;
    border: 1px solid #444;
    width: 100%;
    height: 100%;
}

/* Responsive */
@media (max-width: 768px) {
    body {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        max-height: 40vh;
        border-right: none;
        border-bottom: 1px solid #333;
    }

    .chat-area {
        height: auto;
    }

    .main-title {
        font-size: 2rem;
    }

    .chat-history {
        padding: 0.75rem;
    }

    .chat-message {
        max-width: 90%;
    }

    .input-area input[type="text"] {
        padding: 0.6rem 0.8rem;
    }

    .input-area button {
        padding: 0.6rem 1rem;
    }
}

/* ----- Trang Choice (choice.html) ----- */
.choice-container {
    text-align: center;
    padding: 40px;
    border: 1px solid #333;
    border-radius: 8px;
    background-color: #181825;
    color: #ffffff;
    max-width: 500px;
    margin: auto;
}

.choice-container h1 {
    font-size: 2.2rem;
    margin-bottom: 2rem;
}

.choice-button {
    display: block;
    width: 80%;
    margin: 1.5rem auto;
    padding: 1rem 1.5rem;
    font-size: 1.2rem;
    border-radius: 8px;
}
/* Remove background and padding for user messages that contain only an image */
.chat-message.user-message.image-message {
    background-color: transparent;
    padding: 0;
    justify-content: flex-end;
    align-items: center;
    margin-left: auto;
}

/* Image container styling */
.chat-message.image-message > div {
    width: 240px;
    height: 240px;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #333;
}

.chat-message.image-message img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Product Images styling */
/* Product Messages Styling */
.chat-message.assistant-message.product-message {
    display: flex;
    flex-direction: column;
    background-color: #ffd54f;
    padding: 20px;
    max-width: 90%;
}

.chat-message.assistant-message.product-message::before {
    position: static;
    display: inline-block;
    margin-right: 0.5rem;
    content: "🤖";
    background-color: #d4a100;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    text-align: center;
    line-height: 44px;
    font-size: 26px;
}

.chat-message.assistant-message.product-message .title-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.assistant-message strong.product-title {
    color: #181825;
    display: inline-block;
    vertical-align: middle;
    font-size: 1.2rem;
    font-weight: bold;
    margin-left: 10px;
}

/* Product grid and images inside chat messages */
.chat-message .product-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    width: 100%;
    justify-content: start;
    align-items: flex-start;
}

/* Style for individual product cards */
.chat-message .product-grid > div {
    display: flex;
    flex-direction: column;
    min-height: 180px;
    width: 120px;
}

/* For fewer items, adjust the layout */
.chat-message .product-grid.grid-1-item {
    grid-template-columns: 1fr;
    max-width: 150px;
    margin: 0 auto;
}

.chat-message .product-grid.grid-2-items {
    grid-template-columns: repeat(2, 1fr);
    max-width: 350px;
    margin: 0 auto;
}

.chat-message .product-grid.grid-3-items {
    grid-template-columns: repeat(3, 1fr);
}

.chat-message .product-grid.grid-4-items {
    grid-template-columns: repeat(4, 1fr);
}

.chat-message .product-grid.grid-5-items {
    grid-template-columns: repeat(5, 1fr);
}

/* Product image container */
.chat-message .product-image-container {
    width: 120px;
    height: 120px;
    overflow: hidden;
    border-radius: 10px;
    background-color: #223327;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Product image */
.chat-message .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.chat-message .product-image:hover {
    transform: scale(1.05);
}

/* Product name */
.chat-message .product-name {
    margin-top: 10px;
    font-size: 0.95rem;
    font-weight: bold;
    text-align: center;
    color: #181825;
    width: 120px;
    margin-left: auto;
    margin-right: auto;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.2;
    order: 2;
}

/* Responsive media queries */
@media (max-width: 1200px) {
    .chat-message .product-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .chat-message .product-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .chat-message .product-grid {
        grid-template-columns: 1fr;
    }
}