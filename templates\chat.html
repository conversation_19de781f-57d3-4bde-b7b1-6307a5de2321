<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG Chatbot</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chat.css') }}">
    <style>
        /* Suggested Questions Styles */
        .suggested-questions-area {
            padding: 10px 0;
            margin-bottom: 0;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            position: absolute;
            bottom: 70px; /* Vị trí cố định phía trên khu vực input */
            left: 0;
            right: 0;
            cursor: grab; /* Indicates the area is draggable */
            background-color: #1e1e2e;
            z-index: 10;
            border-top: 1px solid #333;
            border-bottom: 1px solid #333;
        }

        /* Hide scrollbar completely for all browsers */
        .suggested-questions-area::-webkit-scrollbar {
            display: none;
        }

        .suggested-questions-container {
            display: flex;
            flex-wrap: nowrap;
            gap: 8px;
            padding: 0 30px; /* Increased padding to account for shadows */
            width: max-content;
        }

        .suggested-question {
            background-color: #4a5568;
            color: white;
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 0.85em;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.1s;
            display: inline-block;
            white-space: nowrap;
            flex-shrink: 0;
            user-select: none; /* Prevent text selection during drag */
        }

        /* Style when dragging */
        .suggested-questions-area.dragging .suggested-question {
            transform: scale(0.98);
        }

        /* Scroll shadows to indicate more content */
        .scroll-shadow {
            position: absolute;
            top: 0;
            height: 100%;
            width: 30px;
            pointer-events: none;
            z-index: 1;
        }

        .scroll-shadow-left {
            left: 0;
            background: linear-gradient(to right, rgba(30, 30, 46, 0.8), rgba(30, 30, 46, 0));
            opacity: 0;
            transition: opacity 0.3s;
        }

        .scroll-shadow-right {
            right: 0;
            background: linear-gradient(to left, rgba(30, 30, 46, 0.8), rgba(30, 30, 46, 0));
            opacity: 1;
            transition: opacity 0.3s;
        }

        /* Responsive styles for suggested questions */
        @media (max-width: 768px) {
            .suggested-questions-area {
                padding: 8px 0;
                margin-bottom: 8px;
            }

            .suggested-question {
                padding: 6px 10px;
                font-size: 0.8em;
            }

            .scroll-shadow {
                width: 20px;
            }
        }

        .suggested-question:hover {
            background-color: #2d3748;
        }

        /* Menu display styles */
        .menu-header {
            margin-bottom: 10px;
        }

        .menu-header h3 {
            font-size: 1.2em;
            color: #4299e1;
            margin: 0;
            padding: 0;
        }

        .menu-categories {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .menu-category-item {
            background-color: #2d3748;
            border-radius: 8px;
            padding: 10px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .menu-category-item:hover {
            background-color: #4a5568;
        }

        .category-name {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .category-description {
            font-size: 0.9em;
            color: #cbd5e0;
            margin-bottom: 5px;
        }

        .category-count {
            font-size: 0.8em;
            color: #a0aec0;
        }

        .menu-products {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .menu-product-item {
            background-color: #2d3748;
            border-radius: 8px;
            padding: 12px;
        }

        .product-name {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 3px;
        }

        .product-description {
            font-size: 0.9em;
            color: #e2e8f0;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .product-details {
            display: flex;
            justify-content: flex-end;
            font-size: 0.9em;
        }

        .product-category {
            color: #a0aec0;
            font-style: italic;
        }

        /* Simple menu styles */
        .menu-item {
            margin: 0;
            padding: 0;
            border: none;
            background: none;
            font-size: 1em;
            line-height: 1.4;
            cursor: pointer;
            color: #000000;
            display: block;
        }

        .menu-item:hover {
            text-decoration: underline;
        }

        .menu-title {
            font-weight: bold;
            margin: 0;
            padding: 0;
            display: block;
            font-size: 1.1em;
            border: none;
            color: #000000;
            line-height: 1.4;
        }

        .menu-subtitle {
            margin: 0;
            padding: 0;
            display: block;
            font-weight: normal;
            color: #000000;
            line-height: 1.4;
        }

        .menu-product {
            margin: 0;
            padding: 0;
            line-height: 1.4;
            white-space: normal;
            word-wrap: break-word;
            display: block;
            width: 100%;
            border: none;
        }

        .product-name {
            font-weight: normal;
            margin: 0;
            padding: 0;
            display: inline;
            color: #000000;
        }

        .product-description {
            display: block;
            margin: 0 0 0 10px;
            padding: 0;
            color: #000000;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Sidebar Toggle Button -->
            <button id="sidebar-toggle">☰</button> <br>

            <!-- Phần thông tin người dùng -->
            <div class="sidebar-user-section">
                {% if user_info %}
                    <h3><span style="font-size: 0.95em;">👤</span> Thông tin người dùng</h3>
                    <div style="background-color: #1e1e2e; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                        <p style="font-size: 1.33em; color: #facc15; margin: 0 0 0.5rem 0;">{{ user_info.name }}</p>
                        <p style="font-size: 1em; color: #cbd5e1; margin: 0;">Khách hàng thân thiết</p>
                        {% if purchase_history %}
                            <p style="font-size: 0.95em; color: #cbd5e1; margin: 0.5rem 0 0 0;">Số đơn hàng: <strong>{{ purchase_history|length }}</strong></p>
                        {% endif %}
                    </div>

                    <h3><span style="font-size: 0.95em;">🛍️</span> Lịch sử mua hàng</h3>
                    <div class="purchase-history">
                        {% if purchase_history %}
                            {% for item in purchase_history %}
                                <div class="purchase-history-item">
                                    <strong>{{ item.date }}</strong><br>
                                    Sản phẩm: {{ item.product }}<br>
                                    Số lượng: {{ item.quantity }}<br>
                                    Giá: {{ item.price }}đ<br>
                                    Đánh giá: {{ item.rate }}⭐<br>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p>Chưa có lịch sử mua hàng.</p>
                        {% endif %}
                    </div>
                {% else %}
                    <h3><span style="font-size: 0.95em;">👤</span> Khách ẩn danh</h3>
                    <p>Bạn đang chat ẩn danh. Hãy <a href="{{ url_for('register') }}" style="color: #facc15;">đăng ký</a> để có trải nghiệm cá nhân hóa.</p>
                    <a href="{{ url_for('register') }}" style="text-decoration: none; display: block; margin-bottom: 12px;">
                        <button class="logout-button" style="background-color: #3b82f6; width: 100%; margin-bottom: 0;">📝 Đăng ký tài khoản mới</button>
                    </a>
                {% endif %}

                <a href="{{ url_for('logout') }}" style="text-decoration: none;">
                    <button class="logout-button">🚪 Đăng xuất</button>
                </a>
            </div>

            <!-- Phần thông tin thêm -->
            <div class="sidebar-info-section">
                <hr style="border-color: #444; margin: 0.8rem 0;">
                <h3><span style="font-size: 0.95em;">ℹ️</span> Thông tin thêm</h3>
                <h4>Giới thiệu</h4>
                <p style="font-size: 0.85em;">Chatbot RAG (Tạo sinh có tăng cường truy xuất dữ liệu)</p>
                <h4>Tính năng</h4>
                <ul style="font-size: 0.85em;">
                    <li>🔍 Tìm kiếm ngữ nghĩa thông minh</li>
                    <li>🧠 Phản hồi theo ngữ cảnh</li>
                    <li>📚 Tích hợp kho tri thức</li>
                    <li>👤 Gợi ý cá nhân hóa</li>
                    <li>📸 Xác thực khuôn mặt</li>
                </ul>
                <h4>Cách sử dụng</h4>
                <ol style="font-size: 0.85em;">
                    <li>💬 Nhập câu hỏi hoặc yêu cầu vào ô chat.</li>
                    <li>➡️ Nhấn <b>Gửi</b> hoặc <b>Enter</b> để gửi.</li>
                    <li>🖼️ Tải ảnh lên bằng nút
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" style="vertical-align: middle; margin-bottom: 2px;" viewBox="0 0 16 16">
                          <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                          <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                        </svg>
                        cạnh ô nhập.
                    </li>
                    <li>🤖 Xem phản hồi và hình ảnh sản phẩm từ chatbot.</li>
                </ol>
                <p style="font-size: 0.8em; margin-top: 0.5rem;">Cần hỗ trợ? Liên hệ: 📧 <EMAIL></p>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="chat-area">
            <div class="title-area">
                <h1 class="main-title">🤖 CHATBOT</h1>
            </div>

            <!-- Chat History -->
            <div class="chat-history" id="chat-history">
                <div class="chat-message assistant-message">
                    <p>Xin chào {{ user_info.name if user_info else 'bạn' }}! Tôi có thể giúp gì cho bạn?</p>
                </div>
            </div>

            <!-- Suggested Questions Area -->
            <div class="suggested-questions-area">
                <div class="suggested-questions-container">
                    <div class="suggested-question" data-query-id="menu_categories">
                        <span>📋 Xem danh mục đồ uống</span>
                    </div>
                    <div class="suggested-question" data-query-id="classic_espresso">
                        <span>☕ Classic Espresso Drinks</span>
                    </div>
                    <div class="suggested-question" data-query-id="coffee">
                        <span>☕ Coffee</span>
                    </div>
                    <div class="suggested-question" data-query-id="frappuccino_coffee">
                        <span>🥤 Frappuccino Blended Coffee</span>
                    </div>
                    <div class="suggested-question" data-query-id="frappuccino_creme">
                        <span>🍦 Frappuccino Blended Crème</span>
                    </div>
                    <div class="suggested-question" data-query-id="iced_beverages">
                        <span>🧊 Shaken Iced Beverages</span>
                    </div>
                    <div class="suggested-question" data-query-id="signature_espresso">
                        <span>✨ Signature Espresso Drinks</span>
                    </div>
                    <div class="suggested-question" data-query-id="smoothies">
                        <span>🥭 Smoothies</span>
                    </div>
                    <div class="suggested-question" data-query-id="tea">
                        <span>🍵 Tazo Tea Drinks</span>
                    </div>
                </div>
                <!-- Add shadow indicators for scrolling -->
                <div class="scroll-shadow scroll-shadow-left"></div>
                <div class="scroll-shadow scroll-shadow-right"></div>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <input type="text" id="user-input" placeholder="Bạn cần tôi giúp gì? 🤔" autocomplete="off">
                <button id="send-button">Gửi</button>
                <input type="file" id="image-upload" accept="image/*" style="display:none;">
                <button id="upload-button" title="Tải ảnh lên">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                      <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                      <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                    </svg>
                </button>
                <button id="mic-button" title="Chat bằng giọng nói">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M3.5 6.5A.5.5 0 0 1 4 7v1a4 4 0 0 0 8 0V7a.5.5 0 0 1 1 0v1a5 5 0 0 1-4.5 4.975V15h3a.5.5 0 0 1 0 1h-7a.5.5 0 0 1 0-1h3v-2.025A5 5 0 0 1 3 8V7a.5.5 0 0 1 .5-.5z"/>
                      <path d="M10 8a2 2 0 1 1-4 0V3a2 2 0 1 1 4 0v5zM8 0a3 3 0 0 0-3 3v5a3 3 0 0 0 6 0V3a3 3 0 0 0-3-3z"/>
                    </svg>
                </button>
                <div class="voice-dropdown">
                    <button id="voice-settings-button" title="Cài đặt giọng nói">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                          <path d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z"/>
                        </svg>
                    </button>
                    <div id="voice-dropdown-content" class="voice-dropdown-content">
                        <div style="font-weight: bold; padding: 5px; border-bottom: 1px solid #ddd;">Giọng đọc:</div>
                        <div class="voice-option" onclick="changeVoice('huyen')">Nữ</div>
                        <div class="voice-option" onclick="changeVoice('khanhlq')">Nam</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
</body>
</html>